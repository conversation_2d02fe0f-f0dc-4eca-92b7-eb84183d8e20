{"id": "90492570-7d2a-4481-a06a-2399b6ede354", "prevId": "ec4787b6-c1ad-4c48-9496-e2dfc772a343", "version": "7", "dialect": "postgresql", "tables": {"public.articles": {"name": "articles", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": true}, "publish_date": {"name": "publish_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": false}, "language": {"name": "language", "type": "text", "primaryKey": false, "notNull": false}, "location": {"name": "location", "type": "text", "primaryKey": false, "notNull": false}, "completeness": {"name": "completeness", "type": "text", "primaryKey": false, "notNull": false}, "relevance": {"name": "relevance", "type": "text", "primaryKey": false, "notNull": false}, "summary": {"name": "summary", "type": "text", "primaryKey": false, "notNull": false}, "fail_reason": {"name": "fail_reason", "type": "text", "primaryKey": false, "notNull": false}, "source_id": {"name": "source_id", "type": "integer", "primaryKey": false, "notNull": true}, "processed_at": {"name": "processed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"articles_source_id_sources_id_fk": {"name": "articles_source_id_sources_id_fk", "tableFrom": "articles", "tableTo": "sources", "columnsFrom": ["source_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"articles_url_unique": {"name": "articles_url_unique", "nullsNotDistinct": false, "columns": ["url"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.newsletter": {"name": "newsletter", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"newsletter_email_unique": {"name": "newsletter_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.reports": {"name": "reports", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "total_articles": {"name": "total_articles", "type": "integer", "primaryKey": false, "notNull": true}, "total_sources": {"name": "total_sources", "type": "integer", "primaryKey": false, "notNull": true}, "used_articles": {"name": "used_articles", "type": "integer", "primaryKey": false, "notNull": true}, "used_sources": {"name": "used_sources", "type": "integer", "primaryKey": false, "notNull": true}, "tldr": {"name": "tldr", "type": "text", "primaryKey": false, "notNull": false}, "model_author": {"name": "model_author", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sources": {"name": "sources", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "scrape_frequency": {"name": "scrape_frequency", "type": "integer", "primaryKey": false, "notNull": true, "default": 2}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true}, "last_checked": {"name": "last_checked", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"sources_url_unique": {"name": "sources_url_unique", "nullsNotDistinct": false, "columns": ["url"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}