{"id": "37b78e24-fd39-4aa4-8d3d-729d85bf93e2", "prevId": "36890a7e-f6b8-4002-99c7-88e5bfe38f79", "version": "7", "dialect": "postgresql", "tables": {"public.articles": {"name": "articles", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": true}, "publish_date": {"name": "publish_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": false}, "language": {"name": "language", "type": "text", "primaryKey": false, "notNull": false}, "location": {"name": "location", "type": "text", "primaryKey": false, "notNull": false}, "completeness": {"name": "completeness", "type": "text", "primaryKey": false, "notNull": false}, "relevance": {"name": "relevance", "type": "text", "primaryKey": false, "notNull": false}, "source_id": {"name": "source_id", "type": "integer", "primaryKey": false, "notNull": true}, "processed_at": {"name": "processed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"articles_source_id_sources_id_fk": {"name": "articles_source_id_sources_id_fk", "tableFrom": "articles", "tableTo": "sources", "columnsFrom": ["source_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"articles_url_unique": {"name": "articles_url_unique", "nullsNotDistinct": false, "columns": ["url"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sources": {"name": "sources", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "scrape_frequency": {"name": "scrape_frequency", "type": "integer", "primaryKey": false, "notNull": true, "default": 2}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true}, "last_checked": {"name": "last_checked", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"sources_url_unique": {"name": "sources_url_unique", "nullsNotDistinct": false, "columns": ["url"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}