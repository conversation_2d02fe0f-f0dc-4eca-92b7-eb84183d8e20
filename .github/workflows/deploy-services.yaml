name: Deploy services
on:
  push:
    branches:
      - main
jobs:
  deploy:
    runs-on: ubuntu-latest
    timeout-minutes: 10
    steps:
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v4
        with:
          version: 9.15.4

      - uses: actions/setup-node@v4
        with:
          node-version: '22.14.0'
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install

      - name: Check Git status before generating migrations
        run: git status --porcelain
        id: pre_migration_status
        working-directory: 'packages/database'

      - name: Generate migrations
        run: pnpm generate
        working-directory: 'packages/database'

      - name: Check if new migrations were created
        id: check_migrations
        run: |
          git status --porcelain
          if [[ $(git status --porcelain | grep -E "^\?\?" | wc -l) -gt 0 ]]; then
            echo "New migration files were created during CI. Please run 'pnpm generate' locally and commit the changes."
            echo "new_files=true" >> $GITHUB_OUTPUT
            exit 1
          fi
          if [[ $(git status --porcelain | grep -E "^M" | wc -l) -gt 0 ]]; then
            echo "Existing migration files were modified during CI. Please run 'pnpm generate' locally and commit the changes."
            echo "modified_files=true" >> $GITHUB_OUTPUT
            exit 1
          fi
          echo "No new or modified migration files detected."
        working-directory: 'packages/database'

      - name: Run database migrations
        run: pnpm db:migrate
        working-directory: 'packages/database'
        env:
          DATABASE_URL: ${{ secrets.DATABASE_URL }}

      - name: Build & Deploy Worker
        uses: cloudflare/wrangler-action@v3
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          packageManager: pnpm
          workingDirectory: 'apps/scrapers'
          environment: production
          secrets: |
            GOOGLE_BASE_URL
            GOOGLE_API_KEY
            DATABASE_URL
            MERIDIAN_SECRET_KEY
        env:
          GOOGLE_BASE_URL: ${{ secrets.GOOGLE_BASE_URL }}
          GOOGLE_API_KEY: ${{ secrets.GOOGLE_API_KEY }}
          DATABASE_URL: ${{ secrets.DATABASE_URL }}
          MERIDIAN_SECRET_KEY: ${{ secrets.MERIDIAN_SECRET_KEY }}

      - name: Build Nuxt Application
        run: pnpm build --filter=@meridian/frontend # Or 'yarn generate', ensure this matches your static build script in package.json (npx nuxi generate)
        env:
          NUXT_DATABASE_URL: ${{ secrets.DATABASE_URL }}

      # - name: Publish to Cloudflare Pages
      #   uses: cloudflare/wrangler-action@v3 # Use the official Cloudflare Wrangler action
      #   with:
      #     apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }} # Use the secret token
      #     accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }} # Use the secret account ID
      #     command: pages deploy apps/frontend/dist --project-name=meridian-frontend --branch=main
      #     secrets: |
      #       NUXT_DATABASE_URL
      #   env:
      #     NUXT_DATABASE_URL: ${{ secrets.DATABASE_URL }}
      # Replace YOUR_CLOUDFLARE_PAGES_PROJECT_NAME with the actual name from Step 3
      # The --branch flag tells Cloudflare which production branch this deployment corresponds to
