<script lang="ts" setup>
import SubscriptionForm from '../../components/SubscriptionForm.vue';

const config = useRuntimeConfig();
const { $md } = useNuxtApp();

// Constants
const WORDS_PER_MINUTE = 300;

// Route and brief data extraction
const slug = useRoute().path.split('/').pop()?.replaceAll('_', '/');
if (slug === undefined) {
  throw createError({
    statusCode: 404,
    statusMessage: 'Brief not found',
  });
}

const briefData = getReportBySlug(slug);

const url = ref(`/`);

// Reading progress state
const readingProgress = ref(0);
let scrollListener: () => void;

/**
 * Calculates estimated reading time in minutes
 */
const estimateReadingTime = (content: string): number => {
  const wordCount = content.trim().split(/\s+/).length;
  return Math.ceil(wordCount / WORDS_PER_MINUTE);
};

// SEO metadata
const formatDate = computed(() => {
  const date = briefData.value.date;
  return date ? `${date.month.toLowerCase()} ${date.day}, ${date.year}` : '';
});

useSEO({
  title: `${briefData.value.title.toLowerCase()} | meridian`,
  description: `brief for ${formatDate.value}`,
  ogImage: `${config.public.WORKER_API}/openGraph/brief?title=${encodeURIComponent(briefData.value.title)}&date=${encodeURIComponent(briefData.value.createdAt.getTime())}&articles=${briefData.value.usedArticles}&sources=${briefData.value.usedSources}`,
  ogUrl: `https://news.iliane.xyz/briefs/${slug}`,
});

// Lifecycle hooks
onMounted(() => {
  // Initialize scroll progress tracking
  scrollListener = () => {
    const scrollTop = document.documentElement.scrollTop;
    const scrollHeight = document.documentElement.scrollHeight - document.documentElement.clientHeight;
    readingProgress.value = scrollHeight > 0 ? (scrollTop / scrollHeight) * 100 : 0;
  };

  window.addEventListener('scroll', scrollListener);
});

onUnmounted(() => {
  window.removeEventListener('scroll', scrollListener);
});
</script>

<template>
  <!-- Reading progress bar -->
  <div class="fixed top-0 left-0 w-full h-1 z-50">
    <div
      class="h-full bg-black dark:bg-white transition-all duration-150 ease-out"
      :style="{ width: `${readingProgress}%` }"
    />
  </div>

  <div>
    <header class="mb-8">
      <h1 class="text-4xl font-bold mb-3">
        {{ briefData.title }}
      </h1>
      <div class="flex text-sm text-gray-600 items-center space-x-2">
        <time>{{ briefData.date?.month.toLowerCase() }} {{ briefData.date?.day }}, {{ briefData.date?.year }}</time>
        <span>•</span>
        <p>{{ estimateReadingTime(briefData.content) }} min read</p>
      </div>
    </header>

    <article class="prose text-justify w-full" v-html="$md.render(briefData.content)" />

    <div class="mt-16 mb-8">
      <div class="h-px w-full bg-gray-300 mb-8" />
      <div class="flex flex-col text-center gap-8">
        <!-- Brief stats -->
        <div class="grid grid-cols-2 gap-y-6 gap-x-12 text-sm">
          <div>
            <p class="text-gray-600 mb-1">total articles</p>
            <p class="font-bold text-base">{{ briefData.totalArticles || '-' }}</p>
          </div>
          <div>
            <p class="text-gray-600 mb-1">total sources</p>
            <p class="font-bold text-base">{{ briefData.totalSources || '-' }}</p>
          </div>
          <div>
            <p class="text-gray-600 mb-1">used articles</p>
            <p class="font-bold text-base">{{ briefData.usedArticles || '-' }}</p>
          </div>
          <div>
            <p class="text-gray-600 mb-1">used sources</p>
            <p class="font-bold text-base">{{ briefData.usedSources || '-' }}</p>
          </div>
        </div>

        <div class="text-sm">
          <p class="text-gray-600 mb-1">final brief generated by</p>
          <p class="font-bold text-base">{{ briefData.model_author }}</p>
        </div>

        <!-- Subscription area -->
        <div class="mt-4 pt-8 border-t border-gray-300">
          <SubscriptionForm />
        </div>
      </div>
    </div>
  </div>
</template>
