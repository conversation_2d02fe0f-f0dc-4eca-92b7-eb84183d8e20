{"name": "@meridian/frontend", "private": true, "type": "module", "license": "MIT", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://iliane.xyz"}, "scripts": {"dev": "nuxt dev", "build": "nuxt build", "preview": "nuxt preview", "postinstall": "nuxt prepare", "typecheck": "nuxt typecheck"}, "devDependencies": {"@heroicons/vue": "^2.2.0", "@meridian/database": "workspace:*", "@nuxtjs/color-mode": "3.5.2", "@tailwindcss/typography": "^0.5.10", "@tailwindcss/vite": "^4.0.13", "@types/markdown-it": "^14.1.2", "@unhead/vue": "^2.0.0-rc.1", "@vscode/markdown-it-katex": "^1.1.1", "markdown-it": "^14.1.0", "markdown-it-color": "^2.1.1", "markdown-it-deflist": "^3.0.0", "nuxt": "^3.16.0", "tailwindcss": "^4.0.13", "unstorage": "^1.0.0", "vue": "^3.5.13", "vue-router": "^4.5.0", "vue-tsc": "^2.2.8", "wrangler": "^4.1.0", "zod": "^3.24.2"}}