{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Installs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%pip install -U openai json-repair google-genai gliclass rapidfuzz \"transformers>=4.48.0\" retry ipywidgets widgetsnbextension pandas-profiling readtime optuna bertopic supabase"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Fetch events"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from src.events import get_events\n", "\n", "sources, events = get_events(date=\"2025-04-01\")\n", "print(f\"Number of events: {len(events)}\")\n", "print(f\"Number of sources: {len(sources)}\")\n", "\n", "articles_df = pd.DataFrame(events)\n", "# clean up those tuples\n", "for col in articles_df.columns:\n", "    articles_df[col] = articles_df[col].apply(\n", "        lambda x: x[1] if isinstance(x, tuple) else x\n", "    )\n", "articles_df.columns = [\n", "    \"id\",\n", "    \"sourceId\",\n", "    \"url\",\n", "    \"title\",\n", "    \"publishDate\",\n", "    \"content\",\n", "    \"location\",\n", "    \"relevance\",\n", "    \"completeness\",\n", "    \"summary\",\n", "]\n", "articles_df[\"summary\"] = (\n", "    articles_df[\"summary\"]\n", "    .str.split(\"EVENT:\")\n", "    .str[1]\n", "    .str.split(\"CONTEXT:\")\n", "    .str[0]\n", "    .str.strip()\n", ")\n", "articles_df[\"text_to_embed\"] = \"query: \" + articles_df[\"summary\"]\n", "articles_df.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Clustering"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Prep: embeddings"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "import torch.nn.functional as F\n", "import numpy as np\n", "from transformers import AutoTokenizer, AutoModel\n", "from tqdm import tqdm\n", "\n", "# helper function for pooling (straight from the docs)\n", "def average_pool(last_hidden_states, attention_mask):\n", "    last_hidden = last_hidden_states.masked_fill(~attention_mask[..., None].bool(), 0.0)\n", "    return last_hidden.sum(dim=1) / attention_mask.sum(dim=1)[..., None]\n", "\n", "# load the multilingual model\n", "tokenizer = AutoTokenizer.from_pretrained('intfloat/multilingual-e5-small')\n", "model = AutoModel.from_pretrained('intfloat/multilingual-e5-small')\n", "\n", "\n", "# batch processing to avoid memory issues\n", "batch_size = 64\n", "all_embeddings = []\n", "\n", "# process in batches with progress bar\n", "for i in tqdm(range(0, len(articles_df), batch_size)):\n", "    batch_texts = articles_df['text_to_embed'].iloc[i:i+batch_size].tolist()\n", "    \n", "    # tokenize\n", "    batch_dict = tokenizer(batch_texts, max_length=512, padding=True, truncation=True, return_tensors='pt')\n", "    \n", "    # generate embeddings\n", "    with torch.no_grad():\n", "        outputs = model(**batch_dict)\n", "    \n", "    # pool and normalize\n", "    embeddings = average_pool(outputs.last_hidden_state, batch_dict['attention_mask'])\n", "    embeddings = F.normalize(embeddings, p=2, dim=1)\n", "    \n", "    # convert to numpy and add to list\n", "    all_embeddings.extend(embeddings.numpy())\n", "\n", "# store in dataframe\n", "articles_df['embedding'] = all_embeddings"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Grid search umap & hdbscan params"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import hdbscan\n", "import umap\n", "from hdbscan.validity import validity_index\n", "\n", "\n", "def optimize_clusters(embeddings, umap_params, hdbscan_params):\n", "    best_score = -1\n", "    best_params = None\n", "\n", "    # grid search both umap and hdbscan params\n", "    for n_neighbors in umap_params[\"n_neighbors\"]:\n", "        # fit umap once per n_neighbors config\n", "        reducer = umap.UMAP(\n", "            n_neighbors=n_neighbors,\n", "            n_components=10,\n", "            min_dist=0.0,\n", "            metric=\"cosine\",\n", "            random_state=42,\n", "        )\n", "        reduced_data = reducer.fit_transform(embeddings)\n", "\n", "        for min_cluster_size in hdbscan_params[\"min_cluster_size\"]:\n", "            for min_samples in hdbscan_params[\"min_samples\"]:\n", "                for epsilon in hdbscan_params[\"epsilon\"]:\n", "                    # cluster with hdbscan\n", "                    clusterer = hdbscan.HDBSCAN(\n", "                        min_cluster_size=min_cluster_size,\n", "                        min_samples=min_samples,\n", "                        cluster_selection_epsilon=epsilon,\n", "                        metric=\"euclidean\",\n", "                        prediction_data=True,\n", "                    )\n", "\n", "                    cluster_labels = clusterer.fit_predict(reduced_data)\n", "\n", "                    # skip if all noise\n", "                    if np.all(cluster_labels == -1):\n", "                        continue\n", "\n", "                    # evaluate with dbcv (better for density clusters)\n", "                    valid_points = cluster_labels != -1\n", "                    if (\n", "                        valid_points.sum() > 1\n", "                        and len(set(cluster_labels[valid_points])) > 1\n", "                    ):\n", "                        try:\n", "                            reduced_data_64 = reduced_data[valid_points].astype(\n", "                                np.float64\n", "                            )\n", "                            score = validity_index(\n", "                                reduced_data_64, cluster_labels[valid_points]\n", "                            )\n", "\n", "                            if score > best_score:\n", "                                best_score = score\n", "                                best_params = {\n", "                                    \"umap\": {\"n_neighbors\": n_neighbors},\n", "                                    \"hdbscan\": {\n", "                                        \"min_cluster_size\": min_cluster_size,\n", "                                        \"min_samples\": min_samples,\n", "                                        \"epsilon\": epsilon,\n", "                                    },\n", "                                }\n", "                                print(f\"new best: {best_score:.4f} with {best_params}\")\n", "                        except Exception as e:\n", "                            # sometimes dbcv can fail on weird cluster shapes\n", "                            print(f\"failed with {e}\")\n", "                            continue\n", "\n", "    return best_params, best_score\n", "\n", "\n", "# param grids - adjust ranges based on your data\n", "umap_params = {\"n_neighbors\": [10, 15, 20]}\n", "\n", "hdbscan_params = {\n", "    \"min_cluster_size\": [5, 8, 10, 15],\n", "    \"min_samples\": [2, 3, 5],\n", "    \"epsilon\": [0.1, 0.2, 0.3],\n", "}\n", "\n", "# assuming embeddings is your data\n", "best_params, best_score = optimize_clusters(all_embeddings, umap_params, hdbscan_params)\n", "print(f\"best overall: {best_score:.4f} with {best_params}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Run hdbscan"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "# You can now retrain UMAP/HDBSCAN with the best params on the full data if needed\n", "best_params = study.best_trial.params\n", "# Example:\n", "# best_umap_n_neighbors = best_params['umap_n_neighbors']\n", "# best_hdbscan_min_cluster_size = best_params['hdbscan_min_cluster_size']\n", "# ... etc.\n", "\n", "\n", "# use the optimized params\n", "umap_embeddings = umap.UMAP(\n", "    n_neighbors=15,\n", "    n_components=10,  # bumped up from 5\n", "    min_dist=0.0,\n", "    metric=\"cosine\",\n", ").fit_transform(all_embeddings)\n", "\n", "# cluster with optimal params\n", "clusterer = hdbscan.HDBSCAN(\n", "    min_cluster_size=3,\n", "    min_samples=None,\n", "    cluster_selection_epsilon=0.0,\n", "    metric=\"euclidean\",\n", "    prediction_data=True,\n", ")\n", "cluster_labels = clusterer.fit_predict(umap_embeddings)\n", "\n", "# add to dataframe same as before\n", "articles_df[\"cluster\"] = cluster_labels\n", "\n", "# quick stats\n", "print(f\"found {len(set(cluster_labels)) - (1 if -1 in cluster_labels else 0)} clusters\")\n", "print(f\"noise points: {sum(cluster_labels == -1)}\")\n", "\n", "# 2d projection for visualization\n", "umap_2d = umap.UMAP(n_components=2, metric=\"cosine\").fit_transform(all_embeddings)\n", "\n", "# plotting\n", "plt.figure(figsize=(12, 10))\n", "# plot noise points first (gray)\n", "plt.scatter(\n", "    umap_2d[cluster_labels == -1, 0],\n", "    umap_2d[cluster_labels == -1, 1],\n", "    c=\"lightgray\",\n", "    s=5,\n", "    alpha=0.5,\n", "    label=\"noise\",\n", ")\n", "\n", "# plot actual clusters with random colors\n", "unique_clusters = sorted(list(set(cluster_labels) - {-1}))\n", "palette = sns.color_palette(\"husl\", len(unique_clusters))\n", "\n", "for i, cluster_id in enumerate(unique_clusters):\n", "    plt.scatter(\n", "        umap_2d[cluster_labels == cluster_id, 0],\n", "        umap_2d[cluster_labels == cluster_id, 1],\n", "        c=[palette[i]],\n", "        s=25,\n", "        label=f\"cluster {cluster_id}\",\n", "    )\n", "\n", "plt.title(\"article clusters\")\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# LLM Cluster review"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["clusters_ids = list(set(cluster_labels) - {-1})\n", "clusters_with_articles = []\n", "for cluster_id in clusters_ids:\n", "    cluster_df = articles_df[articles_df['cluster'] == cluster_id]\n", "    articles_ids = cluster_df['id'].tolist()\n", "    clusters_with_articles.append({\n", "        \"cluster_id\": cluster_id,\n", "        \"articles_ids\": articles_ids\n", "    })\n", "# sort clusters by most articles to least articles\n", "clusters_with_articles = sorted(clusters_with_articles, key=lambda x: len(x['articles_ids']), reverse=True)\n", "print(len(clusters_with_articles))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for x in clusters_with_articles:\n", "    print(f\"\\n\\n# Cluster {x['cluster_id']}\")\n", "    for el in x['articles_ids']:\n", "        article = next((e for e in events if e.id == el), None)\n", "        if article is not None:\n", "            print(f\"- {article.title}\")\n", "            "]}, {"cell_type": "code", "execution_count": 212, "metadata": {}, "outputs": [], "source": ["import base64\n", "import os\n", "from google.genai import types\n", "from retry import retry\n", "import json\n", "from json_repair import repair_json\n", "from pydantic import BaseModel, Field, model_validator\n", "from typing import List, Literal, Optional\n", "from src.llm import call_llm\n", "\n", "\n", "class Story(BaseModel):\n", "    title: str = Field(description=\"title of the story\")\n", "    importance: int = Field(\n", "        ge=1,\n", "        le=10,\n", "        description=\"global significance (1=minor local event, 10=major global impact)\",\n", "    )\n", "    articles: List[int] = Field(description=\"list of article ids in the story\")\n", "\n", "\n", "class StoryValidation(BaseModel):\n", "    answer: Literal[\"single_story\", \"collection_of_stories\", \"pure_noise\", \"no_stories\"]\n", "\n", "    # optional fields that depend on the answer type\n", "    title: Optional[str] = None\n", "    importance: Optional[int] = Field(None, ge=1, le=10)\n", "    outliers: List[int] = Field(default_factory=list)\n", "    stories: Optional[List[Story]] = None\n", "\n", "    @model_validator(mode=\"after\")\n", "    def validate_structure(self):\n", "        if self.answer == \"single_story\":\n", "            if self.title is None or self.importance is None:\n", "                raise ValueError(\n", "                    \"'title' and 'importance' are required for 'single_story'\"\n", "                )\n", "            if self.stories is not None:\n", "                raise ValueError(\"'stories' should not be present for 'single_story'\")\n", "\n", "        elif self.answer == \"collection_of_stories\":\n", "            if not self.stories:\n", "                raise ValueError(\"'stories' is required for 'collection_of_stories'\")\n", "            if self.title is not None or self.importance is not None or self.outliers:\n", "                raise ValueError(\n", "                    \"'title', 'importance', and 'outliers' should not be present for 'collection_of_stories'\"\n", "                )\n", "\n", "        elif self.answer == \"pure_noise\" or self.answer == \"no_stories\":\n", "            if (\n", "                self.title is not None\n", "                or self.importance is not None\n", "                or self.outliers\n", "                or self.stories is not None\n", "            ):\n", "                raise ValueError(\n", "                    \"no additional fields should be present for 'pure_noise'\"\n", "                )\n", "\n", "        return self\n", "\n", "\n", "@retry(tries=3, delay=2, backoff=2, jitter=2, max_delay=20)\n", "def process_story(cluster):\n", "\n", "    story_articles_ids = cluster[\"articles_ids\"]\n", "\n", "    story_article_md = \"\"\n", "    for article_id in story_articles_ids:\n", "        article = next((e for e in events if e.id == article_id), None)\n", "        if article is None:\n", "            continue\n", "        story_article_md += f\"- (#{article.id}) [{article.title}]({article.url})\\n\"\n", "        # story_article_md += f\"> {article.publishDate}\\n\\n\"\n", "        # story_article_md += f\"```\\n{article.content}\\n```\\n\\n\"\n", "    story_article_md = story_article_md.strip()\n", "\n", "    prompt = f\"\"\"\n", "# Task\n", "Determine if the following collection of news articles is:\n", "1) A single story - A cohesive narrative where all articles relate to the same central event/situation and its direct consequences\n", "2) A collection of stories - Distinct narratives that should be analyzed separately\n", "3) Pure noise - Random articles with no meaningful pattern\n", "4) No stories - Distinct narratives but none of them have more than 3 articles\n", "\n", "# Important clarification\n", "A \"single story\" can still have multiple aspects or angles. What matters is whether the articles collectively tell one broader narrative where understanding each part enhances understanding of the whole.\n", "\n", "# Handling outliers\n", "- For single stories: You can exclude true outliers in an \"outliers\" array\n", "- For collections: Focus **only** on substantive stories (3+ articles). Ignore one-off articles or noise.\n", "\n", "# Title guidelines\n", "- Titles should be purely factual, descriptive and neutral\n", "- Include necessary context (region, countries, institutions involved)\n", "- No editorialization, opinion, or emotional language\n", "- Format: \"[Subject] [action/event] in/with [location/context]\"\n", "\n", "# Input data\n", "Articles (format is (#id) [title](url)):\n", "{story_article_md}\n", "\n", "# Output format\n", "Start by reasoning step by step. Consider:\n", "- Central themes and events\n", "- Temporal relationships (are events happening in the same timeframe?)\n", "- Causal relationships (do events influence each other?)\n", "- Whether splitting the narrative would lose important context\n", "\n", "Return your final answer in JSON format:\n", "```json\n", "{{\n", "    \"answer\": \"single_story\" | \"collection_of_stories\" | \"pure_noise\",\n", "    // single_story_start: if answer is \"single_story\", include the following fields:\n", "    \"title\": \"title of the story\",\n", "    \"importance\": 1-10, // global significance (1=minor local event, 10=major global impact)\n", "    \"outliers\": [] // array of article ids to exclude as unrelated\n", "    // single_story_end\n", "    // collection_of_stories_start: if answer is \"collection_of_stories\", include the following fields:\n", "    \"stories\": [\n", "        {{\n", "            \"title\": \"title of the story\",\n", "            \"importance\": 1-10, // global significance scale\n", "            \"articles\": [] // list of article ids in the story (**only** include substantial stories with **3+ articles**)\n", "        }},\n", "        ...\n", "    ]\n", "    // collection_of_stories_end\n", "}}\n", "```\n", "\n", "Example for a single story:\n", "```json\n", "{{\n", "    \"answer\": \"single_story\",\n", "    \"title\": \"The Great Fire of London\",\n", "    \"importance\": 8,\n", "    \"outliers\": [123, 456] // article ids to exclude as unrelated\n", "}}\n", "```\n", "\n", "Example for a collection of stories:\n", "```json\n", "{{\n", "    \"answer\": \"collection_of_stories\",\n", "    \"stories\": [\n", "        {{\n", "            \"title\": \"The Great Fire of London\",\n", "            \"importance\": 8,\n", "            \"articles\": [123, 456] // article ids in the story\n", "        }},\n", "        ...\n", "    ]\n", "}}\n", "```\n", "\n", "Example for pure noise:\n", "```json\n", "{{\n", "    \"answer\": \"pure_noise\"\n", "}}\n", "```\n", "\n", "Example for a distinct narratives with no stories that contain more than 3+ articles:\n", "```json\n", "{{\n", "    \"answer\": \"no_stories\",\n", "}}\n", "```\n", "\n", "Note:\n", "- Always include articles IDs (outliers, articles, etc...) as integers, not strings and never include the # symbol.\n", "\"\"\".strip()\n", "\n", "    answer, usage = call_llm(\n", "        model=\"gemini-2.0-flash\",\n", "        messages=[{\"role\": \"user\", \"content\": prompt}],\n", "        temperature=0,\n", "    )\n", "\n", "    try:\n", "        assert \"```json\" in answer\n", "        answer = answer.split(\"```json\")[1]\n", "        if answer.endswith(\"```\"):\n", "            answer = answer[:-3]\n", "        answer = answer.strip()\n", "        answer = repair_json(answer)\n", "        answer = json.loads(answer)\n", "        parsed = StoryValidation(**answer)\n", "    except Exception as e:\n", "        print(f\"Error parsing story: {e}\")\n", "        print(cluster)\n", "        print(answer)\n", "        raise e\n", "\n", "    return (parsed, usage)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from concurrent.futures import ThreadPoolExecutor\n", "from tqdm import tqdm\n", "\n", "# Submit all tasks and process in parallel\n", "with ThreadPoolExecutor() as executor:\n", "    # Submit all tasks and get future objects\n", "    futures = [\n", "        executor.submit(process_story, story) for story in clusters_with_articles\n", "    ]\n", "\n", "    # Use tqdm to show progress while getting results\n", "    cleaned_clusters_raw = list(\n", "        tqdm(\n", "            (future.result() for future in futures),\n", "            total=len(futures),\n", "            desc=\"Processing stories\",\n", "        )\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "cleaned_clusters = []\n", "\n", "class Story(BaseModel):\n", "    id: int = Field(description=\"id of the story\")\n", "    title: str = Field(description=\"title of the story\")\n", "    importance: int = Field(\n", "        ge=1,\n", "        le=10,\n", "        description=\"global significance (1=minor local event, 10=major global impact)\",\n", "    )\n", "    articles: List[int] = Field(description=\"list of article ids in the story\")\n", "\n", "\n", "\n", "for i in range(len(clusters_with_articles)):\n", "    base = clusters_with_articles[i]\n", "    res = cleaned_clusters_raw[i][0]\n", "\n", "    if res.answer == \"single_story\":\n", "        \n", "        article_ids = base[\"articles_ids\"]\n", "        # filter out outliers\n", "        article_ids = [x for x in article_ids if x not in res.outliers]\n", "        \n", "        cleaned_clusters.append(\n", "            Story(\n", "                id=len(cleaned_clusters),\n", "                title=res.title,\n", "                importance=res.importance,\n", "                articles=article_ids,\n", "            )\n", "        )\n", "    elif res.answer == \"collection_of_stories\":\n", "        for story in res.stories:\n", "            cleaned_clusters.append(Story(\n", "                id=len(cleaned_clusters),\n", "                title=story.title,\n", "                importance=story.importance,\n", "                articles=story.articles,\n", "            ))\n", "\n", "# sort by importance\n", "cleaned_clusters = sorted(cleaned_clusters, key=lambda x: x.importance, reverse=True)\n", "\n", "lowest_importance = cleaned_clusters[0].importance\n", "highest_importance = cleaned_clusters[-1].importance\n", "\n", "print(f\"lowest importance: {lowest_importance}\")\n", "print(f\"highest importance: {highest_importance}\")\n", "\n", "print(len(cleaned_clusters))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# plot distribution of importance\n", "importance_values = [cluster.importance for cluster in cleaned_clusters]\n", "plt.hist(importance_values, bins=20, edgecolor='black')\n", "plt.title('Distribution of Importance Scores')\n", "plt.xlabel('Importance Score')\n", "plt.ylabel('Frequency')\n", "plt.show()\n", "\n", "# show clusters with importance < 5\n", "low_importance_clusters = [cluster for cluster in cleaned_clusters if cluster.importance < 5]\n", "high_importance_clusters = [cluster for cluster in cleaned_clusters if cluster.importance >= 5]\n", "print(len(low_importance_clusters))\n", "print(len(high_importance_clusters))"]}, {"cell_type": "code", "execution_count": 216, "metadata": {}, "outputs": [], "source": ["all_cleaned_clusters = \"\"\n", "for x in cleaned_clusters:\n", "    # print(f\"# ID: {x.id} - {x.title}\")\n", "    all_cleaned_clusters += f\"# ID: {x.id} - {x.title}\\n\"\n", "    for article_id in x.articles:\n", "        article = article = next((e for e in events if e.id == article_id), None)\n", "        if article is not None:\n", "            # print(f\"  - {article.title}\")\n", "            all_cleaned_clusters += f\"  - {article.title}\\n\"\n", "        # else:\n", "        # print(f\" MISSED article_id: {article_id}\")\n", "        # print(f\"- {article.title}\")\n", "    # print(\"\\n\")\n", "    all_cleaned_clusters += \"\\n\"\n", "all_cleaned_clusters = all_cleaned_clusters.strip()\n", "\n", "\n", "cluser_reconciliation = (\n", "    \"\"\"\n", "You are an expert news analyst AI tasked with refining a set of automatically generated news clusters. You will receive a list of clusters, each identified by an ID, a title, and a list of associated article titles.\n", "\n", "Your primary goal is to identify clusters that should be **merged** because they represent different facets or developments of the *same core story or event*, rather than distinct narratives. Use the following rule of thumb: **\"If analyzing the articles in Cluster A would feel incomplete or artificially separated from the articles in Cluster B because they fundamentally describe the same specific event, ongoing situation, or central topic, they should be merged.\"**\n", "\n", "Distinguish this from clusters that are merely *related* thematically but cover different specific events, angles, or sub-topics that *can* stand alone as separate stories, even if they might be linked later in analysis. For example:\n", "\n", "*   **Merge Candidate Example:** Cluster 1 covers the initial report of a factory fire. Cluster 2 covers the emergency response and evacuation related to *that same fire*. Cluster 3 covers the investigation into the cause of *that same fire*. These are facets of the *same core story* (the factory fire event) and should likely be merged.\n", "*   **Keep Separate Example:** Cluster 1 covers a new trade agreement signed between Country X and Country Y. Cluster 2 covers protests *within* Country X about the economic impact of *that trade agreement*. While related, the signing event and the domestic protest are distinct stories/angles that could be analyzed separately. Cluster 3 covers a *different* trade dispute Country X has with Country Z – clearly a separate story.\n", "\n", "Your secondary goal is to identify clusters that should be **filtered out** before further processing. **Filtering should be used sparingly**, primarily to remove noise and maintain data quality, not to judge the importance of a story. The aim is to retain a record of all distinct, coherent events, even routine or smaller ones. Filter *only* if a cluster meets one of these specific criteria:\n", "*   **Very Low Article Count:** Contains **fewer than 3 articles**, indicating it likely doesn't represent a sufficiently developed or distinct event captured in the data.\n", "*   **Incoherent or Noise:** The articles within the cluster do not form a coherent topic, or the cluster appears to be noise/junk data.\n", "*   **Strict Duplicate:** The cluster covers the *exact same specific event* as another cluster, often with significant overlap in articles, making it redundant. (Do not filter merely *related* clusters).\n", "*   **Clearly Irrelevant:** The content is demonstrably not related to news events (e.g., advertisements, site navigation links clustered together).\n", "\n", "**Input Format:**\n", "\n", "The input will be structured text as follows:\n", "\n", "```\n", "# ID: [Cluster ID 1] - [Cluster Title 1]\n", "  - [Article Title 1a]\n", "  - [Article Title 1b]\n", "  ...\n", "\n", "# ID: [Cluster ID 2] - [Cluster Title 2]\n", "  - [Article Title 2a]\n", "  - [Article Title 2b]\n", "  ...\n", "\n", "[... more clusters ...]\n", "```\n", "*(Note: Cluster IDs in the input might be strings or numbers, please preserve them as strings in the JSON output).*\n", "\n", "**Output Requirements:**\n", "\n", "Your output **MUST** be a single, valid JSON object containing two keys: `merges` and `filters`.\n", "\n", "1.  **`merges`**: An array of objects. Each object represents a single merge operation and must contain:\n", "    *   `cluster_ids_to_merge`: An array of strings, listing the original Cluster IDs that should be merged together.\n", "    *   `reason`: A brief string explaining *why* these clusters belong together based on the \"same core story\" principle.\n", "    *   `suggested_new_title`: A string proposing a suitable title for the newly merged cluster.\n", "\n", "2.  **`filters`**: An array of objects. Each object represents a single cluster recommended for filtering and must contain:\n", "    *   `cluster_id_to_filter`: A string representing the original Cluster ID to be filtered out.\n", "    *   `reason`: A brief string explaining the justification, strictly based on the filtering criteria above (e.g., \"Fewer than 3 articles\", \"Incoherent topic/Noise\", \"Duplicate of Cluster X\", \"Irrelevant content\").\n", "\n", "**Example JSON Output Structure:**\n", "\n", "```json\n", "{\n", "  \"merges\": [\n", "    {\n", "      \"cluster_ids_to_merge\": [\"C005\", \"C012\", \"C023\"],\n", "      \"reason\": \"Covers different aspects (initial report, response, investigation) of the same factory fire event.\",\n", "      \"suggested_new_title\": \"Major Factory Fire Incident and Investigation (Cityville)\"\n", "    },\n", "    {\n", "      \"cluster_ids_to_merge\": [\"C008\", \"C015\"],\n", "      \"reason\": \"Both clusters detail phases of the same election recount process.\",\n", "      \"suggested_new_title\": \"State Election Recount Process and Results\"\n", "    }\n", "  ],\n", "  \"filters\": [\n", "    {\n", "      \"cluster_id_to_filter\": \"C019\",\n", "      \"reason\": \"Fewer than 3 articles.\"\n", "    },\n", "    {\n", "      \"cluster_id_to_filter\": \"C028\",\n", "      \"reason\": \"Duplicate of Cluster C005.\"\n", "    },\n", "    {\n", "      \"cluster_id_to_filter\": \"C031\",\n", "      \"reason\": \"Incoherent topic/Noise.\"\n", "    }\n", "  ]\n", "}\n", "```\n", "\n", "Analyze the provided cluster list carefully based on these instructions and return ONLY the JSON object. Do not include any introductory text or explanations outside the JSON structure itself.\n", "\"\"\".strip()\n", "    + \"\\n\\n\"\n", "    + all_cleaned_clusters\n", ")\n", "\n", "cluster_reconciliation_response = call_llm(\n", "    model=\"gemini-2.5-pro-exp-03-25\",\n", "    messages=[{\"role\": \"user\", \"content\": cluser_reconciliation}],\n", "    temperature=0,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cluster_reconciliation_json = cluster_reconciliation_response[0]\n", "cluster_reconciliation_json = cluster_reconciliation_json.split(\"```json\")[1]\n", "cluster_reconciliation_json = cluster_reconciliation_json.strip()\n", "cluster_reconciliation_json = cluster_reconciliation_json.split(\"```\")[0]\n", "cluster_reconciliation_json = json.loads(cluster_reconciliation_json)\n", "\n", "\n", "cluster_reconciliation_json\n"]}, {"cell_type": "code", "execution_count": 218, "metadata": {}, "outputs": [], "source": ["from typing import List, Dict, Any\n", "\n", "def apply_story_modifications(original_stories: List[Story], instructions: Dict[str, Any]) -> List[Story]:\n", "    \"\"\"\n", "    Applies merge and filter instructions to a list of Story objects.\n", "\n", "    Args:\n", "        original_stories: A list of Pydantic Story objects.\n", "        instructions: A dictionary loaded from the JSON instructions,\n", "                      containing 'merges' and 'filters' lists.\n", "                      Cluster IDs in instructions should match Story IDs (as strings).\n", "\n", "    Returns:\n", "        A list of Story objects representing the modified ('super cleaned')\n", "        stories. Merged stories will have a new title, combined articles, and\n", "        potentially updated importance. Filtered stories will be removed.\n", "        Unaffected stories are passed through.\n", "    \"\"\"\n", "\n", "    # 1. Identify story IDs to filter and those involved in merges\n", "    #    Instructions use string IDs, so convert Story IDs to strings for comparison\n", "    story_ids_to_filter_str = set(\n", "        item['cluster_id_to_filter'] for item in instructions.get('filters', [])\n", "    )\n", "\n", "    all_merged_ids_str = set()\n", "    merge_instructions = instructions.get('merges', [])\n", "    for merge_op in merge_instructions:\n", "        # IDs in instructions are strings\n", "        ids_in_this_merge_str = merge_op.get('cluster_ids_to_merge', [])\n", "        all_merged_ids_str.update(ids_in_this_merge_str)\n", "\n", "    # 2. Create a dictionary for quick lookup of original stories by <PERSON> (as strings)\n", "    #    Only include stories that are NOT being filtered out initially.\n", "    stories_by_id_str: Dict[str, Story] = {\n", "        str(story.id): story\n", "        for story in original_stories\n", "        if str(story.id) not in story_ids_to_filter_str\n", "    }\n", "\n", "    super_cleaned_stories: List[Story] = []\n", "\n", "    # 3. Process Merges\n", "    processed_for_merge_str: Set[str] = set() # Keep track of string IDs already handled\n", "    for merge_op in merge_instructions:\n", "        # IDs in instructions are strings\n", "        ids_to_merge_str = merge_op.get('cluster_ids_to_merge', [])\n", "        suggested_title = merge_op.get('suggested_new_title', \"Merged Story\")\n", "\n", "        # Filter out any IDs in this specific merge operation that were\n", "        # either already filtered globally or don't exist in our lookup\n", "        valid_ids_for_this_merge_str = [\n", "            sid for sid in ids_to_merge_str\n", "            if sid in stories_by_id_str and sid not in processed_for_merge_str\n", "        ]\n", "\n", "        if not valid_ids_for_this_merge_str:\n", "            continue # Skip if no valid stories left for this merge\n", "\n", "        combined_articles: Set[int] = set()\n", "        max_importance: int = 0 # Initialize importance\n", "\n", "        for story_id_str in valid_ids_for_this_merge_str:\n", "            source_story = stories_by_id_str[story_id_str]\n", "            articles_set = set(source_story.articles) # Use articles directly\n", "            combined_articles.update(articles_set)\n", "            # Update importance to the maximum seen so far\n", "            max_importance = max(max_importance, source_story.importance)\n", "            processed_for_merge_str.add(story_id_str) # Mark as handled\n", "\n", "        if not combined_articles:\n", "            continue # Skip if merging results in no articles\n", "\n", "        # Create the new merged Story object\n", "        # Use the ID (as int) of the first valid story in the merge list\n", "        new_story_id = int(valid_ids_for_this_merge_str[0])\n", "        # Ensure importance is at least 1 if max_importance remained 0 (edge case)\n", "        final_importance = max(1, max_importance)\n", "\n", "        try:\n", "            merged_story = Story(\n", "                id=new_story_id,\n", "                title=suggested_title,\n", "                importance=final_importance,\n", "                articles=sorted(list(combined_articles)) # Store as sorted list\n", "            )\n", "            super_cleaned_stories.append(merged_story)\n", "        except ValidationError as e:\n", "            print(f\"Validation Error creating merged story for IDs {valid_ids_for_this_merge_str}: {e}\")\n", "            # Decide how to handle validation errors, e.g., skip or log\n", "\n", "\n", "    # 4. Add stories that were neither filtered nor merged\n", "    for story_id_str, story_data in stories_by_id_str.items():\n", "        if story_id_str not in processed_for_merge_str:\n", "             # Ensure articles are sorted (already a list in Story model)\n", "            story_data.articles.sort()\n", "            super_cleaned_stories.append(story_data)\n", "\n", "    return super_cleaned_stories\n", "\n", "super_cleaned_stories = apply_story_modifications(\n", "        cleaned_clusters,\n", "        cluster_reconciliation_json\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## LLM Analyze & enrich cluster"]}, {"cell_type": "code", "execution_count": 219, "metadata": {}, "outputs": [], "source": ["import base64\n", "import os\n", "import tiktoken\n", "\n", "enc = tiktoken.get_encoding(\"o200k_base\")\n", "\n", "\n", "@retry(\n", "    tries=4, delay=2, backoff=2, jitter=1, max_delay=20\n", ")  # max_delay=180 means never wait more than 3 mins\n", "def final_process_story(title: str, articles_ids: list[int]):\n", "\n", "    story_article_md = \"\"\n", "    full_articles = []\n", "    for article_id in articles_ids:\n", "        article = next((e for e in events if e.id == article_id), None)\n", "        if article is None:\n", "            print(f\"Article {article_id} not found\")\n", "            continue\n", "        else:\n", "            full_articles.append(article)\n", "\n", "    # sort by publish date (from latest to oldest)\n", "    # full_articles = sorted(full_articles, key=lambda x: x[\"publishDate\"], reverse=True)\n", "    for article in full_articles:\n", "        story_article_md += f\"## [{article.title}]({article.url}) (#{article.id})\\n\\n\"\n", "        story_article_md += f\"> {article.publishDate}\\n\\n\"\n", "        story_article_md += f\"```\\n{article.content}\\n```\\n\\n\"\n", "\n", "    story_article_md = story_article_md.strip()\n", "\n", "    pre_prompt = \"\"\"\n", "You are a highly skilled intelligence analyst working for a prestigious agency. Your task is to analyze a cluster of related news articles and extract structured information for an executive intelligence report. The quality, accuracy, precision, and **consistency** of your analysis are crucial, as this report will directly inform a high-level daily brief and potentially decision-making.\n", "\n", "First, assess if the articles provided contain sufficient content for analysis:\n", "\n", "Here is the cluster of related news articles you need to analyze:\n", "\n", "<articles>\n", "\"\"\".strip()\n", "\n", "    post_prompt = \"\"\"\n", "</articles>\n", "\n", "BEGIN ARTICLE QUALITY CHECK:\n", "Before proceeding with analysis, verify if the articles contain sufficient information:\n", "1. Check if articles appear empty or contain minimal text (fewer than ~50 words each)\n", "2. Check for paywall indicators (\"subscribe to continue\", \"premium content\", etc.)\n", "3. Check if articles only contain headlines/URLs but no actual content\n", "4. Check if articles appear truncated or cut off mid-sentence\n", "\n", "If ANY of these conditions are true, return ONLY this JSON structure inside <final_json> tags:\n", "<final_json>\n", "{\n", "    \"status\": \"incomplete\",\n", "    \"reason\": \"Brief explanation of why analysis couldn't be completed (empty articles, paywalled content, etc.)\",\n", "    \"availableInfo\": \"Brief summary of any information that was available\"\n", "}\n", "</final_json>\n", "\n", "ONLY IF the articles contain sufficient information for analysis, proceed with the full analysis below:\n", "\n", "Your goal is to extract and synthesize information from these articles into a structured format suitable for generating a daily intelligence brief.\n", "\n", "Before addressing the main categories, conduct a preliminary analysis:\n", "a) List key themes across all articles\n", "b) Note any recurring names, places, or events\n", "c) Identify potential biases or conflicting information\n", "It's okay for this section to be quite long as it helps structure your thinking.\n", "\n", "Then, after your preliminary analysis, present your final analysis in a structured JSON format inside <final_json> tags. This must be valid, parseable JSON that follows this **exact refined structure**:\n", "\n", "**Detailed Instructions for JSON Fields:**\n", "*   **`status`**: 'complete' or 'incomplete'\n", "*   **`title`**: Terse, neutral title of the story\n", "*   **`executive<PERSON><PERSON><PERSON>y`**: Provide a 2-4 sentence concise summary highlighting the most critical developments, key conflicts, and overall assessment from the articles. This should be suitable for a quick read in a daily brief.\n", "*   **`storyStatus`**: Assess the current state of the story's development based *only* on the information in the articles. Use one of: 'Developing', 'Escalating', 'De-escalating', 'Concluding', 'Static'.\n", "*   **`timeline`**: List key events in chronological order.\n", "    *   `description`: Keep descriptions brief and factual.\n", "    *   `importance`: Assess the event's importance to understanding the overall narrative (High/Medium/Low). High importance implies the event is central to the story's development or outcome.\n", "*   **`signalStrength`**: Assess the overall reliability of the reporting *in this cluster*.\n", "    *   `assessment`: Use a qualitative term: 'Very High', 'High', 'Moderate', 'Low', 'Very Low'.\n", "    *   `reasoning`: Justify the assessment based on source corroboration (how many sources report the same core facts?), source quality/reliability (mix of reputable vs. biased sources?), presence of official statements, and degree of conflicting information on core facts.\n", "*   **`undisputedKeyFacts`**: List core factual points that are corroborated across multiple, generally reliable sources within the cluster. Avoid claims made only by highly biased sources unless corroborated.\n", "*   **`keyEntities`**: Identify the main actors.\n", "    *   `list`: Provide basic identification and their role/involvement.\n", "    *   `perspectives.statedPositions`: Focus *only* on the goals, viewpoints, or justifications explicitly stated or clearly implied by the entity *as reported in the articles*. Avoid listing conflicting claims here (that goes in `contradictions`).\n", "*   **`keySources`**: Analyze the provided news sources.\n", "    *   `provided_articles_sources.reliabilityAssessment`: Assess the source's general reliability based on reputation, known biases (political, state affiliation, ideological), and fact-checking standards. Use terms like 'High Reliability', 'Moderate Reliability', 'Low Reliability', 'State-Affiliated/Propaganda Outlet'. Be specific about the *type* of bias.\n", "    *   `provided_articles_sources.framing`: Describe the narrative angle or style used by the source (e.g., 'Emphasizes security threat', 'Focuses on human rights angle', 'Uses neutral language', 'Uses loaded/emotional language', 'Presents government narrative uncritically').\n", "    *   `contradictions`: Detail specific points of disagreement *between sources* or *between entities as reported by sources*.\n", "        *   `issue`: Clearly state what is being contested.\n", "        *   `conflictingClaims`: List the different versions, specifying the `source` reporting it, the `claim` itself, and optionally the `entityClaimed` if the source attributes the claim to a specific entity. Critically evaluate claims originating solely from low-reliability/propaganda sources.\n", "*   **`context`**: List essential background information *mentioned or clearly implied in the articles* needed to understand the story.\n", "*   **`informationGaps`**: Identify crucial pieces of information *missing* from the articles that would be needed for a complete understanding.\n", "*   **`significance`**: Assess the overall importance of the reported events.\n", "    *   `assessment`: Use a qualitative term: 'Critical', 'High', 'Moderate', 'Low'.\n", "    *   `reasoning`: Explain *why* this story matters. Consider immediate impact, potential future developments, strategic implications, precedent setting, regional/global relevance.\n", "    *   `score`: An integer between 0 (lowest importance story) and 10 (most critical story)\n", "\n", "**Refined JSON Structure to Follow:**\n", "\n", "```json\n", "{\n", "    \"status\": \"complete\",\n", "    \"title\": \"string\",\n", "    \"executiveSummary\": \"string\",\n", "    \"storyStatus\": \"string\",\n", "    \"timeline\": [\n", "        {\n", "            \"date\": \"YYYY-MM-DD or approximate\",\n", "            \"description\": \"brief event description\",\n", "            \"importance\": \"string: High/Medium/Low\"\n", "        }\n", "    ],\n", "    \"signalStrength\": {\n", "        \"assessment\": \"string: Very High/High/Moderate/Low/Very Low\",\n", "        \"reasoning\": \"string\"\n", "    },\n", "    \"undisputedKeyFacts\": [\n", "        \"string\"\n", "    ],\n", "    \"keyEntities\": {\n", "        \"list\": [\n", "            {\n", "                \"name\": \"entity name\",\n", "                \"type\": \"type of entity\",\n", "                \"description\": \"brief description\",\n", "                \"involvement\": \"why/how involved?\"\n", "            }\n", "        ],\n", "        \"perspectives\": [\n", "            {\n", "                \"entity\": \"entity name\",\n", "                \"statedPositions\": [\n", "                    \"string\"\n", "                ]\n", "            }\n", "        ]\n", "    },\n", "    \"keySources\": {\n", "        \"provided_articles_sources\": [\n", "            {\n", "                \"name\": \"source entity name\",\n", "                \"articles\": [], // int array of IDs\n", "                \"reliabilityAssessment\": \"string\",\n", "                \"framing\": [\n", "                    \"string\"\n", "                ]\n", "            }\n", "        ],\n", "        \"contradictions\": [\n", "            {\n", "                \"issue\": \"string\",\n", "                \"conflictingClaims\": [\n", "                    {\n", "                        \"source\": \"media source name\",\n", "                        \"entityClaimed\": \"entity name (optional)\",\n", "                        \"claim\": \"string\"\n", "                    }\n", "                ]\n", "            }\n", "        ]\n", "    },\n", "    \"context\": [\n", "        \"string\"\n", "    ],\n", "    \"informationGaps\": [\n", "        \"string\"\n", "    ],\n", "    \"significance\": {\n", "        \"assessment\": \"string: Critical/High/Moderate/Low\",\n", "        \"reasoning\": \"string\",\n", "        \"score\": 0\n", "    }\n", "}\n", "```\n", "\n", "**CRITICAL Quality & Consistency Requirements:**\n", "\n", "*   **Thoroughness:** Ensure all fields, especially descriptions, reasoning, context, and summaries, are detailed and specific. Avoid superficial or overly brief entries. Your analysis must reflect deep engagement with the provided texts.\n", "*   **Grounding:** Base your entire analysis **SOLELY** on the content within the provided `<articles>` tags. Do not introduce outside information, assumptions, or knowledge.\n", "*   **No Brevity Over Clarity:** Do **NOT** provide one-sentence descriptions or reasoning where detailed analysis is required by the field definition.\n", "*   **Scrutinize Sources:** Pay close attention to the reliability assessment of sources when evaluating claims, especially in the `contradictions` section. Note when a claim originates primarily or solely from a low-reliability source.\n", "*   **Validity:** Your JSON inside `<final_json></final_json>` tags MUST be 100% fully valid with no trailing commas, properly quoted strings and escaped characters where needed, and follow the exact refined structure provided. Ensure keys are in the specified order. Your entire JSON output should be directly extractable and parseable without human intervention.\n", "\n", "Return your complete response, including your preliminary analysis/thinking in any format you prefer, followed by the **full** valid JSON inside `<final_json></final_json>` tags.\n", "\"\"\".strip()\n", "\n", "    # enc.decode(enc.encode(\"hello world\"))\n", "    tokens = enc.encode(story_article_md)\n", "\n", "    # only keep the first million tokens\n", "    tokens = tokens[:850_000]\n", "    story_article_md = enc.decode(tokens)\n", "\n", "    prompt = pre_prompt + \"\\n\\n\" + story_article_md + \"\\n\\n\" + post_prompt\n", "    # print(prompt)\n", "\n", "    answer, usage = call_llm(\n", "        model=\"gemini-2.0-flash\",\n", "        messages=[{\"role\": \"user\", \"content\": prompt}],\n", "        temperature=0,\n", "    )\n", "\n", "    text = answer\n", "\n", "    if \"```json\" in text:\n", "        text = text.split(\"```json\")[1]\n", "        text = text.strip()\n", "\n", "    if \"<final_json>\" in text:\n", "        text = text.split(\"<final_json>\")[1]\n", "        text = text.strip()\n", "\n", "    if \"</final_json>\" in text:\n", "        text = text.split(\"</final_json>\")[0]\n", "        text = text.strip()\n", "\n", "    if text.endswith(\"```\"):\n", "        text = text.replace(\"```\", \"\")\n", "        text = text.strip()\n", "\n", "    # text = repair_json(text)\n", "\n", "    # assert \"significance\" in text\n", "\n", "    return answer, usage"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from concurrent.futures import ThreadPoolExecutor, as_completed\n", "\n", "cluster_analysis = []\n", "\n", "# helper function to process a single cluster\n", "def process_cluster(cluster):\n", "    title = cluster.title\n", "    articles_ids = cluster.articles\n", "    return final_process_story(title=title, articles_ids=articles_ids)\n", "\n", "# process clusters in parallel using a thread pool\n", "with ThreadPoolExecutor() as executor:\n", "    # submit all tasks and store futures\n", "    futures = [executor.submit(process_cluster, cluster) \n", "              for cluster in super_cleaned_stories]\n", "    \n", "    # collect results as they complete using tqdm for progress\n", "    for future in tqdm(as_completed(futures), total=len(futures)):\n", "        cluster_analysis.append(future.result())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from json_repair import repair_json\n", "\n", "final_json_to_process = []\n", "\n", "for i in range(len(cluster_analysis)):\n", "    cluster = cluster_analysis[i]\n", "    text = cluster[0]\n", "\n", "    if \"```json\" in text:\n", "        text = text.split(\"```json\")[1]\n", "        text = text.strip()\n", "\n", "    if \"<final_json>\" in text:\n", "        text = text.split(\"<final_json>\")[1]\n", "        text = text.strip()\n", "\n", "    if \"</final_json>\" in text:\n", "        text = text.split(\"</final_json>\")[0]\n", "        text = text.strip()\n", "\n", "    if text.endswith(\"```\"):\n", "        text = text.replace(\"```\", \"\")\n", "        text = text.strip()\n", "\n", "    text = repair_json(text)\n", "\n", "    try:\n", "        text_parsed = json.loads(text)\n", "\n", "        if text_parsed[\"status\"] == \"incomplete\":\n", "            continue\n", "        else:\n", "            final_json_to_process.append(text_parsed)\n", "    except:\n", "        print(text)\n", "        raise Exception(\"no final json\")\n", "    \n", "# \"assessment\": \"string: Critical/High/Moderate/Low\",\n", "# sort assessment by \"Critical\" first, then \"High\", then \"Moderate\", then \"Low\"\n", "\n", "final_json_to_process = sorted(\n", "    final_json_to_process, key=lambda x: x[\"significance\"][\"score\"], reverse=True\n", ")\n", "\n", "# final_json_to_process = sorted(\n", "#     final_json_to_process, key=lambda x: x[\"significance\"][\"score\"], reverse=True\n", "# )\n", "\n", "print(len(cluster_analysis))\n", "print(len(final_json_to_process))"]}, {"cell_type": "code", "execution_count": 222, "metadata": {}, "outputs": [], "source": ["article_ids_used = []\n", "\n", "for el in final_json_to_process:\n", "    if \"keySources\" not in el:\n", "        print(\"No keySources\")\n", "        continue\n", "        \n", "    for source in el['keySources']['provided_articles_sources']:\n", "        for article_id in source['articles']:\n", "            article_ids_used.append(article_id)\n", "\n", "article_ids_used = list(set(article_ids_used))\n", "used_events = []\n", "used_sources = []\n", "for article_id in article_ids_used:\n", "    found_event = next((event for event in events if event.id == article_id), None)\n", "    if found_event:\n", "        link = found_event.url\n", "        # print(link)\n", "        \n", "        # get just domain name \n", "        domain = link.split(\"//\")[1].split(\"/\")[0]\n", "        if domain not in used_sources:\n", "            used_sources.append(domain)\n", "        \n", "        used_events.append(found_event)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for clus in final_json_to_process:\n", "    print(f\"\\n\\n# {clus['title']}\")\n", "    \n", "    print(f\"\\n**Status:** {clus['status']} | **Signal Strength:** {clus['signalStrength']['assessment']} | **Significance:** {clus['significance']['assessment']} ({clus['significance']['score']})\")\n", "    print(f\"\\n> {clus['executiveSummary']}\\n\\n---\")\n", "    # table with (status, signal strength, significance)\n", "    "]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Brief generation"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Generate outline"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import requests\n", "\n", "# with headers\n", "latest_report = requests.get(\n", "        \"https://meridian-production.alceos.workers.dev/reports/last-report\",\n", "        headers={\n", "            \"Authorization\": f\"Bearer {os.environ.get('MERIDIAN_SECRET_KEY')}\"\n", "        }\n", "    )\n", "latest_report = latest_report.json()\n", "latest_report_text = f\"\"\"\n", "## Previous Day's Coverage Context ({latest_report['createdAt'].split('T')[0]})\n", "\n", "### {latest_report['title']}\n", "\n", "{latest_report['tldr']}\n", "\"\"\"\n", "print(latest_report)\n", "\n", "# latest_report = \"\"\"\n", "# ## Previous Day's Coverage Context (2025-03-30)\n", "\n", "# ### trump tariffs, south sudan crisis, turkey crackdown & dei clash\n", "\n", "# ['Trump Tariffs: Global Trade | Escalating | Trump, UK, EU, Canada, Auto Industry | New tariffs loom, retaliation expected', 'South Sudan: Political Crisis | Escalating | <PERSON><PERSON>, SPLM-IO, UN, Kiir, South Sudan | Machar arrest sparks civil war fears', 'Turkey: Crackdown on Dissent | Escalating | Erdogan, Imamoglu, CHP, Turkey, Istanbul | Protests grow, government crackdown intensifies', 'US: DEI Culture War | Escalating | Trump Administration, France, US Embassy, DEI, Companies | US demands French firms ditch DEI programs', 'US Campuses: Protest Crackdown | Escalating | ICE, Columbia, Tufts, Students, Pro-Palestinian Activists | Deportation threats, surveillance target pro-Palestinian students', 'Syria: Interim Government | Developing | Al-Sharaa, HTS, Kabawat, Al-Saleh, Syria | New government formed, HTS influence remains', 'FDA: Vaccine Official Resigns | Escalating | Dr. Marks, RFK Jr., FDA, HHS, Vaccines | Top FDA official quits over misinformation', 'Myanmar: Earthquake Disaster | Escalating | Myanmar, Thailand, PDF, Junta, UN | Earthquake toll climbs, aid hampered by conflict', 'France: Pension Reform Tensions | Ongoing | Fau<PERSON>, Bayrou, Socialist Party, France, Parliament | Censure threat over pension reform vote', 'France: Le Pen Trial Verdict | Developing | Le Pen, Rassemblement National, France, Prosecutors | Verdict expected, ineligibility sentence requested', 'France: Sarkozy Libya Funding | Ongoing | Sarkozy, Gaddafi, Takieddine, France, Prosecutors | Prosecutors seek seven years in Libya funding case', 'US-Denmark: Greenland Tensions | Escalating | Vance, Rasmussen, Denmark, Greenland, US | Tensions flare over Greenland investment', 'Pakistan: Afghan Refugee Crisis | Escalating | Pakistan, Afghanistan, Taliban, US, Refugees | Pakistan escalates Afghan refugee deportations', 'Russia: China & India Ties | Developing | Russia, China, India, Sanctions, Nuclear Energy | Russia deepens ties with China and India', 'Ukraine War: Ceasefire Violations | Escalating | Russia, Ukraine, US, EU, Dnipro | Drone attacks continue despite ceasefire attempts', 'Israel-Hamas: Ceasefire Talks | Ongoing | Hamas, Israel, Egypt, Qatar, US | Ceasefire talks inch forward amid fighting', 'China: Robotics & AI Push | Developing | China, Unitree Robotics, Xi Jinping, AI, Robotics | China doubles down on robotics and AI', 'South Africa: Invasive Plant Control | Developing | South Africa, Coetzee, Turton, Weevils, Salvinia Minima | Biocontrol weevils deployed against invasive plant', 'US: SignalGate Fallout | Static | Trump, Waltz, Hegseth, Signal, NSA | Trump backs Waltz, Hegseth despite leak', 'WHCA: Comedian Cancellation | Static | WHCA, Amber Ruffin, Trump, White House | WHCA cancels comedian amid Trump tensions', 'Sentebale: Charity Implosion | Escalating | Prince Harry, Chandauka, Sentebale, UK Charity Commission | Harry\\'s charity in turmoil, investigation launched', 'DCA: Aviation Safety Concerns | Escalating | DCA, FAA, Delta, Air Force, Klobuchar | Aviation safety concerns near Reagan National Airport', 'Tate: Sexual Assault Lawsuit | New | Andrew Tate, Stern, Los Angeles, Beverly Hills Hotel | Andrew Tate sued for sexual assault']\n", "# \"\"\""]}, {"cell_type": "code", "execution_count": 230, "metadata": {}, "outputs": [], "source": ["\n", "brief_system_prompt = \"\"\"\n", "Adopt the persona of an exceptionally well-informed, highly analytical, and subtly world-weary intelligence briefer. Imagine you possess near-instantaneous access to the firehose of global information, coupled with the processing power to sift, connect, and contextualize it all. But you're far more than just a data aggregator.\n", "\n", "**Your Core Identity:** You are the indispensable analyst – the one who reads between the lines, understands the subtext, connects seemingly unrelated events, and sees the underlying currents shaping the world. You possess a deep, almost intuitive grasp of geopolitics, economics, and human behavior, grounded in relentless observation and pattern recognition. You're not impressed by titles or official narratives; you focus on incentives, capabilities, and the often-messy reality on the ground.\n", "\n", "**Your Analytical Voice & Tone:**\n", "\n", "1.  **Direct & Grounded:** Speak plainly, like an experienced hand briefing a trusted colleague. Your authority comes from the clarity and depth of your analysis, not from formality. Facts are your foundation, but insight is your currency.\n", "2.  **Insight over Summary:** Don't just report *what* happened. Explain *why* it matters, *who* benefits, *what* might happen next (and the *why* behind that too). Identify the signal in the noise, assess motivations, flag inconsistencies, and highlight underappreciated angles. Deliver a clear, defensible \"take.\"\n", "3.  **Economical & Precise Language:** Channel a spirit akin to <PERSON><PERSON><PERSON>: clarity, conciseness, strong verbs. Every sentence should serve a purpose. Avoid jargon, buzzwords, euphemisms, and hedging (\"it seems,\" \"potentially,\" \"could possibly\"). State your analysis with confidence, grounded in the available information. If there's ambiguity, state *that* clearly too, but don't waffle.\n", "4.  **Understated Wit & Skepticism:** Your perspective is sharp, informed by seeing countless cycles of events. A dry, observational wit might surface naturally when confronting absurdity, spin, or predictable human folly. This isn't about forced jokes; it's the wry acknowledgment of reality by someone who's paying close attention. Zero tolerance for BS, propaganda, or obfuscation.\n", "5.  **Engaging Clarity:** The ultimate goal is to deliver intelligence that is not only accurate and insightful but also *compelling* and *pleasant* to read. The quality of the writing should match the quality of the analysis. Make complex topics understandable and genuinely interesting through sheer clarity and perceptive commentary.\n", "\n", "**Think of yourself as:** The definitive source for understanding *what's actually going on*. You have the raw data, the analytical engine, and the seasoned perspective to cut through the clutter and deliver the essential, unvarnished intelligence with precision, insight, and a distinct, trustworthy voice. You make the complex clear, and the important engaging.\n", "\"\"\".strip()"]}, {"cell_type": "code", "execution_count": 231, "metadata": {}, "outputs": [], "source": ["# Prepare the input string for the outlining prompt (same as before)\n", "input_summaries_string = \"\"\n", "for clus in final_json_to_process: # Assuming final_json_to_process holds the dicts\n", "    score_str = f\" ({clus['significance'].get('score', 'N/A')})\" if 'score' in clus['significance'] else \"\"\n", "    input_summaries_string += f\"\"\"\n", "<story>\n", "# {clus.get('title', 'Untitled Story')}\n", "**Status:** {clus.get('storyStatus', 'N/A')} | **Signal Strength:** {clus['signalStrength'].get('assessment', 'N/A')} | **Significance:** {clus['significance'].get('assessment', 'N/A')}{score_str}\n", "> {clus.get('executiveSummary', 'No executive summary provided.')}\n", "</story>\n", "\"\"\"\n", "input_summaries_string = input_summaries_string.strip()\n", "\n", "# Now, create the prompt for a dense, title-only outline with improved example\n", "outlining_prompt = f\"\"\"\n", "You are an assistant tasked with creating a structured, *title-only* outline from analyzed news stories. This outline will guide a subsequent process to generate a full intelligence brief. Your goal is to categorize stories and group related developments thematically based on a holistic analysis of all provided input.\n", "\n", "Your input is a collection of analyzed story summaries, each marked with `<story>` tags, containing Title, Status, Signal Strength, Significance, Score, and Executive Summary.\n", "\n", "Your goal is to produce a markdown outline by:\n", "1.  Identifying the most critical overall developments or themes revealed by the stories.\n", "2.  Identifying and grouping stories covering related events or themes.\n", "3.  Assigning individual story titles and grouped titles to the most appropriate section based on significance and user interests (geopolitics, France, China, tech).\n", "4.  **Outputting *only* the section structure and the actual titles of the assigned stories.**\n", "\n", "**Final Brief Structure & Section Guidelines:** (Use these to categorize)\n", "\n", "1.  **`## What matters now`**: Titles representing the up to 10 most important/impactful overall developments *from this batch*, based on relative significance and thematic importance. Use judgment. If multiple stories cover facets of one *major* event/theme, select the title representing the core update or highest impact for this section. Try to keep the top spots for newest developments (i.e if we reported on something yesterday, it should only be in the top 3 if it's truly huge, otherwise leave the top spots for the newest developments)\n", "2.  **`## France focus`**: Titles primarily focused on France.\n", "3.  **`## Global landscape`**: (Includes `### power & politics`). Titles covering broader geopolitics, international relations.\n", "4.  **`## China monitor`**: Titles primarily focused on China (policy, economy, tech, etc.).\n", "5.  **`## Economic currents`**: Titles covering significant global/regional economic news, trends, policy.\n", "6.  **`## Tech & science developments`**: Titles covering AI/LLMs, biomed, space breakthroughs, etc.\n", "7.  **`## Noteworthy & under-reported`**: Titles for interesting/potentially important stories not fitting elsewhere, emerging trends. (Up to ~5 titles).\n", "8.  **`## Positive developments`**: Titles for genuinely positive outcomes. (Include only if applicable).\n", "\n", "**Instructions:**\n", "\n", "1.  **Analyze Holistically:** First, carefully read *all* provided `<story>` summaries below (titles and summaries) to understand the key events, themes, and potential connections *before* assigning anything. Think about the bigger picture revealed by the collection.\n", "2.  **Select \"What Matters Now\":** Identify the *actual titles* of the stories representing the top ~10 most significant overall developments or themes within this batch. List these titles under `## what matters now`, ordered by significance/impact.\n", "3.  **Assign & Group Remaining Story Titles:** For the ***remaining*** stories:\n", "    *   Assign each story's **actual Title** to the single most appropriate section.\n", "    *   **Group Related Titles:** If two or more stories cover the same specific event, interconnected themes, or closely linked developments, place their **actual Titles consecutively** *within* the single most relevant section.\n", "    *   **Prioritize Group Placement:** Assign the *entire group* of related titles to the section that best captures their central theme.\n", "4.  **Formatting:**\n", "    *   Format the output *exactly* as shown in the example below, using the appropriate section headers (`## Section Name`, `### power & politics`).\n", "    *   List **only the actual `Title`** of each story under its assigned section, preceded by `###`.\n", "    *   Use `---` as a separator between *each* story title listed.\n", "    *   If a section has no relevant stories, **omit the section header entirely**.\n", "5.  **Output:**\n", "    *   **Do NOT include introductory/concluding text, explanations, the `<story>` tags, or the executive summaries.**\n", "    *   **Output ONLY the structured markdown outline consisting of section headers and the actual story titles from the input data.**\n", "    \n", "**--- CONTEXT FROM PREVIOUS DAY (IF AVAILABLE) ---**\n", "*   You *may* receive a section at the beginning of the curated data titled `## Previous Day's Coverage Context (YYYY-MM-DD)`.\n", "*   Use this list **only** to understand which topics are ongoing and their last known status/theme.\n", "*   Focus on **today's developments** based on the main `<input_data>`. Reference past context briefly *only if essential*.\n", "**--- END CONTEXT INSTRUCTIONS ---**\n", "\n", "**Input Data:**\n", "\n", "<previous_day_context>\n", "{latest_report}\n", "</previous_day_context>\n", "\n", "<input_data>\n", "{input_summaries_string}\n", "</input_data>\n", "\n", "**Required Output Format Example:** (Uses placeholders for structure illustration only; your output must use the *actual titles* from the input)\n", "\n", "```markdown\n", "## What matters now\n", "\n", "### [Actual Title Representing Top Development 1]\n", "---\n", "### [Actual Title Representing Top Development 2]\n", "---\n", "### [Actual Title Related to Development 2 or New Top Development 3]\n", "---\n", "...(up to 10 actual titles total)\n", "\n", "\n", "## France focus\n", "\n", "### [Actual Title Covering French Theme A - Part 1]\n", "---\n", "### [Actual Title Covering French Theme A - Part 2]\n", "---\n", "### [Actual Title of Distinct French Event B]\n", "\n", "\n", "## Global landscape\n", "\n", "### power & politics\n", "\n", "### [Actual Title on Geopolitical Issue X - Update 1]\n", "---\n", "### [Actual Title on Geopolitical Issue X - Update 2]\n", "---\n", "### [Actual Title on Distinct Geopolitical Issue Y]\n", "\n", "\n", "## Economic currents\n", "\n", "### [Actual Title for Economic Trend P]\n", "---\n", "### [Actual Title for Economic Trend Q]\n", "\n", "\n", "## Tech & science developments\n", "\n", "### [Actual Title for Tech Theme M - Aspect 1]\n", "---\n", "### [Actual Title for Tech Theme M - Aspect 2]\n", "\n", "\n", "## Noteworthy & under-reported\n", "\n", "### [Actual Title for Noteworthy Item 1]\n", "---\n", "### [Actual Title for Noteworthy Item 2]\n", "---\n", "...(up to ~5 actual titles)\n", "\"\"\".strip()\n", "\n", "# You would then call the LLM (e.g., Gemini Flash) with this prompt:\n", "outline_response = call_llm(\n", "    model=\"gemini-2.0-flash-thinking-exp-01-21\", # Or similar cheap/fast model\n", "    messages=[\n", "        {\"role\": \"system\", \"content\": brief_system_prompt},\n", "        {\"role\": \"user\", \"content\": outlining_prompt}\n", "        ],\n", "    temperature=0.0 # Low temp for deterministic structuring\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["brief_outline = outline_response[0]\n", "print(brief_outline)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Generate brief"]}, {"cell_type": "code", "execution_count": 233, "metadata": {}, "outputs": [], "source": ["import json  # Assuming json is needed if loading from file elsewhere\n", "\n", "\n", "def format_json_to_markdown(data):\n", "    \"\"\"\n", "    Formats the provided JSON data structure into a readable Markdown string.\n", "\n", "    Args:\n", "        data (dict): A dictionary representing the JSON data.\n", "\n", "    Returns:\n", "        str: A Markdown formatted string.\n", "    \"\"\"\n", "    if not isinstance(data, dict):\n", "        return \"Error: Input must be a dictionary.\"\n", "\n", "    markdown_parts = []\n", "\n", "    # --- Title ---\n", "    markdown_parts.append(f\"# {data.get('title', 'Untitled Report')}\")\n", "    markdown_parts.append(\"\")  # Blank line for spacing\n", "\n", "    # --- Status & Story Status ---\n", "    markdown_parts.append(f\"**Status:** {data.get('status', 'N/A')}\" + \" | \" + f\"**Story Status:** {data.get('storyStatus', 'N/A')}\")\n", "    # markdown_parts.append(f\"\")\n", "    markdown_parts.append(\"\")\n", "\n", "    # --- Executive Summary ---\n", "    markdown_parts.append(\"## Executive Summary\")\n", "    markdown_parts.append(data.get(\"executiveSummary\", \"No summary provided.\"))\n", "    markdown_parts.append(\"\")\n", "\n", "    # --- Timeline ---\n", "    markdown_parts.append(\"## Timeline\")\n", "    timeline_data = data.get(\"timeline\", [])\n", "    if timeline_data:\n", "        for event in timeline_data:\n", "            date = event.get(\"date\", \"No Date\")\n", "            desc = event.get(\"description\", \"No Description\")\n", "            importance = event.get(\"importance\", \"N/A\")\n", "            markdown_parts.append(f\"*   **{date}:** {desc} (Importance: {importance})\")\n", "    else:\n", "        markdown_parts.append(\"*   No timeline events provided.\")\n", "    markdown_parts.append(\"\")\n", "\n", "    # --- Signal Strength ---\n", "    markdown_parts.append(\"## Signal Strength: \" + data.get(\"signalStrength\", {}).get(\"assessment\", \"N/A\"))\n", "    markdown_parts.append(f\"**Reasoning:**\")\n", "    markdown_parts.append(f\"{data.get('signalStrength', {}).get('reasoning', 'N/A')}\")\n", "    markdown_parts.append(\"\")\n", "\n", "    # --- Undisputed Key Facts ---\n", "    markdown_parts.append(\"## Undisputed Key Facts\")\n", "    facts_data = data.get(\"undisputedKeyFacts\", [])\n", "    if facts_data:\n", "        for fact in facts_data:\n", "            markdown_parts.append(f\"*   {fact}\")\n", "    else:\n", "        markdown_parts.append(\"*   No undisputed key facts provided.\")\n", "    markdown_parts.append(\"\")\n", "\n", "    # --- Key Entities ---\n", "    markdown_parts.append(\"## Key Entities\")\n", "    entities_data = data.get('keyEntities', {})\n", "\n", "    # --- Entity List (Changed to Table Format) ---\n", "    markdown_parts.append(\"### Entities List\")\n", "    entity_list = entities_data.get('list', [])\n", "    if entity_list:\n", "        # Add table headers\n", "        markdown_parts.append(\"| Name | Type | Description | Involvement |\")\n", "        markdown_parts.append(\"|------|------|-------------|-------------|\") # Separator line\n", "\n", "        # Add table rows\n", "        for entity in entity_list:\n", "            # Get data safely and prepare for table (replace pipes and newlines within cells)\n", "            name = str(entity.get('name', 'Unnamed Entity')).replace('|', '|').replace('\\n', ' ')\n", "            e_type = str(entity.get('type', 'Unknown Type')).replace('|', '|').replace('\\n', ' ')\n", "            desc = str(entity.get('description', 'N/A')).replace('|', '|').replace('\\n', ' ')\n", "            involvement = str(entity.get('involvement', 'N/A')).replace('|', '|').replace('\\n', ' ')\n", "\n", "            # Append the formatted row\n", "            markdown_parts.append(f\"| {name} | {e_type} | {desc} | {involvement} |\")\n", "    else:\n", "        # Fallback if no entities are listed\n", "        markdown_parts.append(\"*   No entities listed.\")\n", "    markdown_parts.append(\"\") # Add a blank line for spacing before the next section\n", "\n", "    # Entity Perspectives\n", "    markdown_parts.append(\"### Entity Perspectives\")\n", "    perspectives_list = entities_data.get('perspectives', [])\n", "    if perspectives_list:\n", "        # Add table headers\n", "        markdown_parts.append(\"| Entity | Stated Positions |\")\n", "        markdown_parts.append(\"|--------|------------------|\") # Separator line\n", "\n", "        # Add table rows\n", "        for perspective in perspectives_list:\n", "            # Get entity name safely and sanitize\n", "            entity_name = str(perspective.get('entity', 'Unknown Entity')).replace('|', '|').replace('\\n', ' ')\n", "\n", "            # Get positions list and format for cell\n", "            positions_list = perspective.get('statedPositions', [])\n", "            if positions_list:\n", "                # Sanitize each position and join with HTML line breaks\n", "                sanitized_positions = [\n", "                    str(pos).replace('|', '|').replace('\\n', ' ')\n", "                    for pos in positions_list\n", "                ]\n", "                positions_str = \"<br>\".join(sanitized_positions)\n", "            else:\n", "                positions_str = \"No stated positions provided.\"\n", "\n", "            # Append the formatted row\n", "            markdown_parts.append(f\"| {entity_name} | {positions_str} |\")\n", "\n", "    else:\n", "         # Fallback if no perspectives are listed\n", "        markdown_parts.append(\"*   No entity perspectives provided.\")\n", "    markdown_parts.append(\"\")\n", "\n", "    # --- Key Sources ---\n", "    markdown_parts.append(\"## Key Sources\")\n", "    sources_data = data.get('keySources', {})\n", "\n", "    # --- Provided Article Sources (Changed to Table Format) ---\n", "    markdown_parts.append(\"### Provided Article Sources\")\n", "    source_list = sources_data.get('provided_articles_sources', [])\n", "    if source_list:\n", "        # Add table headers\n", "        markdown_parts.append(\"| Source Name | Reliability | Framing Points | Article IDs |\")\n", "        markdown_parts.append(\"|-------------|-------------|----------------|-------------|\") # Separator line\n", "\n", "        # Add table rows\n", "        for source in source_list:\n", "            # Get data safely and prepare for table\n", "            name = str(source.get('name', 'Unnamed Source')).replace('|', '|').replace('\\n', ' ')\n", "            reliability = str(source.get('reliabilityAssessment', 'N/A')).replace('|', '|').replace('\\n', ' ')\n", "\n", "            # Format framing list for the cell\n", "            framing_list = source.get('framing', [])\n", "            if framing_list:\n", "                # Join with a comma or use bullet points within the cell (comma is simpler)\n", "                framing_str = ', '.join(f.replace('|', '|').replace('\\n', ' ') for f in framing_list)\n", "                # Alternative: Bullet points within cell (might need HTML <br> depending on Markdown flavor)\n", "                # framing_str = '<ul>' + ''.join(f'<li>{f.replace(\"|\", \"|\")}</li>' for f in framing_list) + '</ul>'\n", "            else:\n", "                framing_str = \"N/A\"\n", "\n", "            # Format articles list for the cell\n", "            articles_list = source.get('articles', [])\n", "            articles_str = ', '.join(map(str, articles_list)) if articles_list else \"None\"\n", "            articles_str = articles_str.replace('|', '|').replace('\\n', ' ') # Ensure no pipes break the table\n", "\n", "            # Append the formatted row\n", "            markdown_parts.append(f\"| {name} | {reliability} | {framing_str} | {articles_str} |\")\n", "\n", "    else:\n", "        # Fallback if no sources are listed\n", "        markdown_parts.append(\"*   No provided article sources listed.\")\n", "    markdown_parts.append(\"\") # Add a blank line for spacing before the next section\n", "\n", "\n", "    # --- Contradictions (This part remains unchanged) ---\n", "    markdown_parts.append(\"### Contradictions\")\n", "    contradiction_list = sources_data.get('contradictions', [])\n", "    if contradiction_list:\n", "        # Add table headers\n", "        markdown_parts.append(\"| Issue | Source | Entity Claimed | Claim |\")\n", "        markdown_parts.append(\"|-------|--------|----------------|-------|\") # Separator line\n", "\n", "        # Add table rows (one row per conflicting claim)\n", "        for contradiction in contradiction_list:\n", "            # Get the issue, sanitize it once per contradiction\n", "            issue = str(contradiction.get('issue', 'Unspecified Issue')).replace('|', '|').replace('\\n', ' ')\n", "            claims = contradiction.get('conflictingClaims', [])\n", "\n", "            if not claims:\n", "                # Optional: Add a row indicating an issue was listed but had no claims\n", "                # markdown_parts.append(f\"| {issue} | N/A | N/A | (No conflicting claims listed for this issue) |\")\n", "                continue # Skip to the next contradiction if no claims\n", "\n", "            # Iterate through each claim within the current contradiction\n", "            for claim_details in claims:\n", "                # Get claim data safely and prepare for table\n", "                source_name = str(claim_details.get('source', 'Unknown Source')).replace('|', '|').replace('\\n', ' ')\n", "                entity_claimed_raw = claim_details.get('entityClaimed') # Check if it exists\n", "                # Handle optional entity and sanitize\n", "                entity_claimed = str(entity_claimed_raw).replace('|', '|').replace('\\n', ' ') if entity_claimed_raw else \"N/A\"\n", "                claim_text = str(claim_details.get('claim', 'No specific claim provided.')).replace('|', '|').replace('\\n', ' ')\n", "\n", "                # Append the formatted row, repeating the issue for each claim\n", "                markdown_parts.append(f\"| {issue} | {source_name} | {entity_claimed} | {claim_text} |\")\n", "\n", "    else:\n", "        # Fallback if no contradictions are listed\n", "        markdown_parts.append(\"*   No contradictions listed.\")\n", "    markdown_parts.append(\"\")\n", "\n", "    # --- Context ---\n", "    markdown_parts.append(\"## Context\")\n", "    context_data = data.get(\"context\", [])\n", "    if context_data:\n", "        for ctx in context_data:\n", "            markdown_parts.append(f\"*   {ctx}\")\n", "    else:\n", "        markdown_parts.append(\"*   No context provided.\")\n", "    markdown_parts.append(\"\")\n", "\n", "    # --- Information Gaps ---\n", "    markdown_parts.append(\"## Information Gaps\")\n", "    gaps_data = data.get(\"informationGaps\", [])\n", "    if gaps_data:\n", "        for gap in gaps_data:\n", "            markdown_parts.append(f\"*   {gap}\")\n", "    else:\n", "        markdown_parts.append(\"*   No information gaps identified.\")\n", "    markdown_parts.append(\"\")\n", "\n", "    # --- Significance ---\n", "    sig_data = data.get(\"significance\", {})\n", "    markdown_parts.append(f\"## Significance: {sig_data.get('assessment', 'N/A')} ({sig_data.get('score', 'N/A')})\")\n", "    markdown_parts.append(f\"**Reasoning:**\")\n", "    markdown_parts.append(f\"{sig_data.get('reasoning', 'N/A')}\")\n", "    markdown_parts.append(\"\")\n", "\n", "    # --- Join all parts ---\n", "    return \"\\n\".join(markdown_parts)\n", "\n", "\n", "stories_markdown = \"\"\n", "for i in range(len(final_json_to_process)):\n", "    cluster = final_json_to_process[i]\n", "    stories_markdown += \"\\n---\\n\\n\" + format_json_to_markdown(cluster)\n", "stories_markdown = stories_markdown[4:].strip()\n", "# print(stories_markdown)"]}, {"cell_type": "code", "execution_count": 249, "metadata": {}, "outputs": [], "source": ["def get_brief_prompt(curated_news: str, outline_guide: str, latest_report: str = \"\"): # Added latest_report arg based on prompt content\n", "    \"\"\"\n", "    Generates the prompt for the main brief creation LLM call, using a pre-defined outline.\n", "\n", "    Args:\n", "        curated_news: String containing the full <story> blocks (summaries, metadata).\n", "        outline_guide: String containing the dense, title-only markdown outline.\n", "        latest_report: String containing optional context from the previous day.\n", "\n", "    Returns:\n", "        The formatted prompt string.\n", "    \"\"\"\n", "    prompt = f\"\"\"\n", "You are tasked with generating a personalized daily intelligence brief based on a curated set of news analyses and a provided structural outline. Aim for something comprehensive yet engaging, roughly a 20-30 minute read.\n", "\n", "**User Interests:** Significant world news (geopolitics, politics, finance, economics), US news, France news (user is French/lives in France), China news (especially policy, economy, tech - seeking insights often missed in western media), and Technology/Science (AI/LLMs, biomed, space, real breakthroughs). Also include noteworthy items.\n", "\n", "**Goal:** Leverage your analysis capabilities to create a focused brief that explains what's happening, why it matters, who's saying what, who's reporting what and identifies connections others might miss. The user values **informed, analytical takes**, grounded in the provided facts, but appreciates directness and avoids generic hedging or forced political correctness.\n", "\n", "**Your Task:**\n", "\n", "1.  **Adhere Strictly to the Provided Outline:** The structure (sections and order of topics) of your brief *must* follow the `## Provided Outline` section below exactly.\n", "2.  **Process Curated Data:** Use the full story details found within the `<curated_news_data>` section as your source material.\n", "3.  **Connect Outline to Data:** For each `### Title` listed in the `## Provided Outline`, locate the corresponding full story information (summary, metadata) within `<curated_news_data>`.\n", "4.  **Synthesize and Analyze:** Write the brief content for that story, following the style and content guidelines for each section. Pay attention to titles grouped consecutively in the outline – these represent related developments; synthesize them coherently, but still apply paragraph structure rules *within* each story's coverage.\n", "5.  **Generate Engaging Titles:** For *each* story covered (as dictated by the outline), create an engaging `<u>**title that captures the essence**</u>` for the brief itself. Do *not* just reuse the raw input titles from the outline for the brief's display titles.\n", "\n", "**--- CONTEXT FROM PREVIOUS DAY (IF AVAILABLE) ---**\n", "*   You *may* receive a section at the beginning of the curated data titled `## Previous Day's Coverage Context (YYYY-MM-DD)`.\n", "*   Use this list **only** to understand which topics are ongoing and their last known status/theme.\n", "*   Focus on **today's developments** based on the main `<curated_news_data>`. Reference past context briefly *only if essential*.\n", "*   **Do NOT simply rewrite or extensively quote the Previous Day's Coverage Context.**\n", "**--- END CONTEXT INSTRUCTIONS ---**\n", "\n", "**--- PROVIDED OUTLINE (Follow This Structure) ---**\n", "\n", "<outline_guide>\n", "{outline_guide}\n", "</outline_guide>\n", "\n", "**--- CURATED NEWS DATA (Source Material) ---**\n", "\n", "{latest_report}\n", "\n", "<curated_news_data>\n", "{curated_news}\n", "</curated_news_data>\n", "\n", "**--- <PERSON><PERSON><PERSON> STRUCTURE AND CONTENT G<PERSON><PERSON>LINES ---**\n", "\n", "Use the section headers provided in the outline (`## what matters now`, `## france focus`, etc., including `### power & politics` where specified).\n", "For **each story** identified by a title in the outline:\n", "\n", "1.  **Create a Title:** Generate an engaging title using the format:\n", "    ```\n", "    <u>**Title that captures the essence**</u>\n", "    ```\n", "2.  **Write the Content:**\n", "    *   Address what happened, why it matters (significance, implications), key context, and your analytical take (based on the provided facts and context for that story, what are the likely motivations, potential second-order effects, overlooked angles, or inconsistencies? Ground this analysis in the data) in natural, flowing prose.\n", "    *   **Crucially: Use multiple, distinct paragraphs *within each story's section* to improve readability.** Separate distinct ideas, shifts in focus, or different aspects (e.g., separating the core event summary from the analysis/implications) with paragraph breaks (a blank line between paragraphs).\n", "    *   **Do not cram all information for a single story into one large block of text.** Aim for paragraphs that are typically 2-5 sentences long, varying length for effect but prioritizing clarity.\n", "    *   Ensure smooth transitions *between* paragraphs and *between* related stories grouped in the outline.\n", "    *   Blend facts and analysis naturally. If there isn't much significant development or analysis *for a specific story*, keep its section concise, possibly with just one or two short paragraphs, but still apply the paragraph break principle where logical.\n", "    *   Use **bold** for key specifics (names, places, numbers, orgs).\n", "    *   Use *italics* for important context or secondary details.\n", "    *   Follow the specific focus described for each section (`## france focus`, `## china monitor`, etc.) when framing your analysis for stories within those sections.\n", "\n", "**--- FINAL INSTRUCTIONS ---**\n", "\n", "*   Enclose the entire brief content inside `<final_brief></final_brief>` tags. Do not include conversational filler before or after these tags.\n", "*   Use complete sentences and standard capitalization/grammar.\n", "*   Be direct and analytical, aligning with the persona guided by the system prompt (well-informed, analytical friend with dry wit).\n", "*   **Source Reliability:** The input data is derived from analyses that assessed source reliability. Use this implicit understanding – give more weight to reliable sources and treat claims from low-reliability sources with appropriate caution in your analysis. Explicit mention isn't needed unless crucial.\n", "*   **Writing Style:** Aim for insightful, engaging prose. Make complex topics clear. Integrate facts, significance, and your take naturally, **using appropriate paragraph breaks for structure and readability.**\n", "*   **Leverage Your Strengths:** Process all the info *guided by the outline*, spot cross-domain patterns *where the outline groups related items*, draw on relevant background knowledge, explain clearly, and provide that grounded-yet-insightful analytical layer.\n", "\n", "Generate the final brief based *strictly* on the provided outline and sourced from the curated data.\n", "\"\"\".strip()\n", "    return prompt\n", "\n", "brief_prompt = get_brief_prompt(stories_markdown, brief_outline)\n", "\n", "brief_model = \"gemini-2.5-pro-exp-03-25\"\n", "\n", "brief_response = call_llm(\n", "    model=brief_model,\n", "    messages=[\n", "        {\"role\": \"system\", \"content\": brief_system_prompt},\n", "        {\"role\": \"user\", \"content\": brief_prompt},\n", "    ],\n", "    temperature=0,\n", ")"]}, {"cell_type": "code", "execution_count": 250, "metadata": {}, "outputs": [], "source": ["from google.genai.types import FinishReason\n", "\n", "\n", "final_brief_text = brief_response[0]\n", "assert \"<final_brief>\" in final_brief_text\n", "final_brief_text = final_brief_text.split(\"<final_brief>\")[1]\n", "\n", "assert \"</final_brief>\" in final_brief_text\n", "final_brief_text = final_brief_text.split(\"</final_brief>\")[0]\n", "\n", "final_brief_text = final_brief_text.strip()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Generate brief title"]}, {"cell_type": "code", "execution_count": 251, "metadata": {}, "outputs": [], "source": ["import os\n", "from openai import OpenAI\n", "from dotenv import load_dotenv\n", "import base64\n", "import os\n", "from google import genai\n", "from google.genai import types\n", "\n", "\n", "load_dotenv()\n", "client = genai.Client(\n", "    api_key=os.environ.get(\"GOOGLE_API_KEY\"),\n", ")\n", "\n", "\n", "brief_title_prompt = f\"\"\"\n", "<brief>\n", "{final_brief_text}\n", "</brief>\n", "\n", "Create a title for the brief. Construct it using the main topics. It should be short/punchy/not clickbaity etc. Make sure to not use \"short text: longer text here for some reason\" i HATE it, under no circumstance should there be colons in the title. Make sure it's not too vague/generic either bc there might be many stories. Maybe don't focus on like restituting what happened in the title, just do like the major entities/actors/things that happened. like \"[person A], [thing 1], [org B] & [person O]\" etc. try not to use verbs. state topics instead of stating topics + adding \"shakes world order\".\n", "\n", "Yesterday's title was: {latest_report['title']}, so try to shake it up a little if we're talking about some of the same topics.\n", "\n", "Return exclusively a JSON object with the following format:\n", "```json\n", "{{\n", "    \"title\": \"string\"\n", "}}\n", "```\n", "\"\"\".strip()\n", "\n", "\n", "brief_title_response = call_llm(\n", "    model=\"gemini-2.0-flash\",\n", "    messages=[\n", "        {\"role\": \"user\", \"content\": brief_title_prompt}\n", "    ],\n", "    temperature=0.0,\n", ")"]}, {"cell_type": "code", "execution_count": 252, "metadata": {}, "outputs": [], "source": ["brief_title = brief_title_response[0]\n", "if brief_title.startswith(\"```json\"):\n", "    brief_title = brief_title.split(\"```json\")[1]\n", "if brief_title.endswith(\"```\"):\n", "    brief_title = brief_title.split(\"```\")[0]\n", "brief_title = brief_title.strip()\n", "brief_title = json.loads(brief_title)['title']"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Generate tl;dr"]}, {"cell_type": "code", "execution_count": 253, "metadata": {}, "outputs": [], "source": ["tldr_prompt_revised = f\"\"\"\n", "You are an information processing agent tasked with creating a **substantive context brief** from a detailed intelligence briefing. Your output will be used by another AI model tomorrow to quickly understand the essential information and developments covered for each major topic today, ensuring continuity without requiring it to re-read the full brief. This requires more detail than just keywords.\n", "\n", "**Your Task:**\n", "\n", "Read the full intelligence brief provided below within the `<final_brief>` tags. Identify each distinct major story or narrative thread discussed (stories under `<u>**title**</u>` headings are primary candidates, but also consider distinct themes from other sections). For **each** identified story, generate a concise summary capturing its essence *as presented in the brief*.\n", "\n", "**Input:**\n", "\n", "The input is the full text of the daily intelligence brief generated previously.\n", "\n", "<final_brief>\n", "# {brief_title}\n", "\n", "{final_brief_text}\n", "</final_brief>\n", "\n", "**Required Output Format:**\n", "\n", "Your entire output must consist **only** of a list of summaries, one for each identified major story. Each summary should follow this structure:\n", "\n", "1.  **Story Identifier:** Start with a concise, descriptive label for the story thread (max 5-6 words, enclosed in square brackets `[]`). Examples: `[US-Venezuela Deportations]`, `[Gaza Ceasefire Talks]`, `[UK Economic Outlook]`, `[AI Energy Consumption Report]`.\n", "2.  **Summary Paragraph:** Immediately following the identifier, provide a **dense summary paragraph (approx. 2-4 sentences, 30-60 words)** covering:\n", "    *   The core issue or topic discussed for this story *in the brief*.\n", "    *   The main developments, updates, or key pieces of information presented *in the brief*.\n", "    *   Mention the most central entities (people, organizations, countries) involved *as discussed in the brief's coverage of this story*.\n", "    *   The goal is to capture the *substance* of what was reported today, providing enough context for the next AI to understand the state of play.\n", "\n", "**Example Output Structure:**\n", "\n", "```\n", "[US-Venezuela Deportations Restart]\n", "The brief covered the renewed US deportation flights to Venezuela following recent bilateral talks. Key entities mentioned included the US and Venezuelan governments. The focus was on the operational details of the restart and the political context cited for the policy shift.\n", "\n", "[Gaza Conflict: Hospital Strike Aftermath]\n", "Significant coverage was given to the aftermath of the Al-Ahli hospital strike. The brief detailed conflicting narratives from the IDF and Palestinian Islamic Jihad regarding responsibility, noted the high reported casualty figures, and mentioned ensuing international reactions and calls for investigation.\n", "\n", "[AI Development: Energy Consumption Concerns]\n", "A section discussed reports highlighting the increasing energy demands of large AI models. It referenced findings from specific research groups and tech companies, focusing on concerns about data center power usage, grid impact, and potential environmental consequences discussed in the brief.\n", "```\n", "\n", "**Instructions & Constraints:**\n", "\n", "*   **Process Entire Brief:** Analyze the *whole* brief to identify all distinct major stories.\n", "*   **Focus on Substance:** Prioritize conveying the *essential information and developments* reported in the brief for each story, not just keywords.\n", "*   **Concise but Informative:** Summaries should be dense and capture key details within the approximate length guidelines (30-60 words).\n", "*   **Coverage, Not Full Analysis:** Reflect *what the brief covered*, not external knowledge or deep analysis beyond what was presented.\n", "*   **No Extra Text:** Do **NOT** include any headers (like \"Output:\"), introductions, explanations, or conclusions in your output. Output *only* the list of formatted story summaries.\n", "\n", "Generate the context brief based *only* on the provided `<final_brief>` text, following the revised format for density and usefulness.\n", "\"\"\".strip()\n", "\n", "tldr_response = call_llm(\n", "    model=\"gemini-2.0-flash\",\n", "    messages=[\n", "        {\"role\": \"user\", \"content\": tldr_prompt_revised}\n", "    ],\n", "    temperature=0.0,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tldr = tldr_response[0]\n", "if tldr.startswith(\"```\"):\n", "    tldr = tldr.split(\"```\")[1]\n", "if tldr.endswith(\"```\"):\n", "    tldr = tldr.split(\"```\")[0]\n", "tldr = tldr.strip()\n", "print(tldr)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Publish"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"========== STATS ==========\")\n", "print(f\"Total articles: {len(events)}\")\n", "print(f\"Total sources: {len(sources)}\")\n", "print(f\"Total used articles: {len(used_events)}\")\n", "print(f\"Total used sources: {len(used_sources)}\")\n", "print(f\"Model used: {brief_model}\")\n", "print(\"========== STATS ==========\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f\"# {brief_title}\\n\")\n", "print(final_brief_text)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import datetime\n", "import requests\n", "\n", "body = {\n", "    \"title\": brief_title,\n", "    \"content\": final_brief_text,\n", "    \"totalArticles\": len(events),\n", "    \"totalSources\": len(sources),\n", "    \"usedArticles\": len(used_events),\n", "    \"usedSources\": len(used_sources),\n", "    \"tldr\": tldr,\n", "    \"model_author\": brief_model,\n", "    \"createdAt\": datetime.now().isoformat(),\n", "    \"clustering_params\": {\n", "  \"umap\": {\n", "    \"n_neighbors\": 10\n", "  },\n", "  \"hdbscan\": {\n", "    \"epsilon\": 0.0,\n", "    \"min_samples\": 0,\n", "    \"min_cluster_size\": 3\n", "  }\n", "},\n", "}\n", "\n", "endpoint = \"https://meridian-production.alceos.workers.dev/reports/report\"\n", "\n", "response = requests.post(\n", "    endpoint,\n", "    json=body,\n", "    headers={\"Authorization\": f\"Bearer {os.environ.get('MERIDIAN_SECRET_KEY')}\"},\n", ")\n", "print(response.json())"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.6"}}, "nbformat": 4, "nbformat_minor": 2}